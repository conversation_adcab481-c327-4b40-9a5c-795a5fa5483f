<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Word模板测试</title>
    <link rel="stylesheet" href="/static/css/base.css">
    <link rel="stylesheet" href="/static/css/app.min-1.0.css">
    <style>
        .test-container {
            max-width: 800px;
            margin: 50px auto;
            padding: 30px;
            background: white;
            border-radius: 16px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        }
        
        .test-title {
            text-align: center;
            color: #1B2C42;
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 30px;
        }
        
        .test-description {
            color: #6c757d;
            text-align: center;
            margin-bottom: 40px;
            line-height: 1.6;
        }
        
        .test-button {
            display: block;
            width: 100%;
            max-width: 300px;
            margin: 0 auto;
            padding: 15px 30px;
            background: linear-gradient(135deg, #565EFF 0%, #6c5ce7 100%);
            color: white;
            border: none;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(86, 94, 255, 0.3);
        }
        
        .result-area {
            margin-top: 30px;
            padding: 20px;
            background: #f8f9ff;
            border-radius: 12px;
            border: 1px solid #e8ecf4;
            display: none;
        }
        
        .result-success {
            color: #10b981;
            font-weight: 600;
        }
        
        .result-error {
            color: #ef4444;
            font-weight: 600;
        }
        
        .download-link {
            display: inline-block;
            margin-top: 15px;
            padding: 10px 20px;
            background: #10b981;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .download-link:hover {
            background: #059669;
            transform: translateY(-1px);
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="test-title">Word模板样式测试</h1>
        <p class="test-description">
            点击下面的按钮生成一个测试Word模板，展示新的现代风格设计。<br>
            这个模板将包含与图片中相似的样式和布局。
        </p>
        
        <button class="test-button" onclick="generateTestTemplate()">
            🎨 生成测试Word模板
        </button>
        
        <div id="resultArea" class="result-area">
            <div id="resultMessage"></div>
            <a id="downloadLink" class="download-link" style="display: none;">
                📥 下载Word文件
            </a>
        </div>
    </div>

    <script src="/static/app/js/lib/jquery-3.5.1.min.js"></script>
    <script>
        function generateTestTemplate() {
            const button = document.querySelector('.test-button');
            const resultArea = document.getElementById('resultArea');
            const resultMessage = document.getElementById('resultMessage');
            const downloadLink = document.getElementById('downloadLink');
            
            // 显示加载状态
            button.textContent = '⏳ 正在生成...';
            button.disabled = true;
            resultArea.style.display = 'block';
            resultMessage.innerHTML = '<span class="result-success">正在生成Word模板，请稍候...</span>';
            downloadLink.style.display = 'none';
            
            // 发送请求
            $.ajax({
                url: '/export/test_word_template',
                type: 'POST',
                contentType: 'application/json',
                success: function(response) {
                    if (response.resultCode === '0000') {
                        resultMessage.innerHTML = '<span class="result-success">✅ Word模板生成成功！</span>';
                        downloadLink.href = '/download?file=' + response.resultMsg;
                        downloadLink.style.display = 'inline-block';
                    } else {
                        resultMessage.innerHTML = '<span class="result-error">❌ 生成失败: ' + response.resultMsg + '</span>';
                    }
                },
                error: function(xhr, status, error) {
                    resultMessage.innerHTML = '<span class="result-error">❌ 请求失败: ' + error + '</span>';
                },
                complete: function() {
                    button.textContent = '🎨 生成测试Word模板';
                    button.disabled = false;
                }
            });
        }
    </script>
</body>
</html> 