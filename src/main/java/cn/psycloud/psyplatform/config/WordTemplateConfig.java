package cn.psycloud.psyplatform.config;

import lombok.Data;
import org.springframework.stereotype.Component;

/**
 * Word模板配置类
 * 基于图片样式优化的配置 - 现代化设计
 */
@Component
@Data
public class WordTemplateConfig {

    /**
     * 标题样式配置
     */
    private TitleStyle title = new TitleStyle();

    /**
     * 表格样式配置
     */
    private TableStyle table = new TableStyle();

    /**
     * 图表样式配置
     */
    private ChartStyle chart = new ChartStyle();

    /**
     * 因子样式配置
     */
    private FactorStyle factor = new FactorStyle();

    /**
     * 页面样式配置
     */
    private PageStyle page = new PageStyle();

    /**
     * 装饰样式配置
     */
    private DecorationStyle decoration = new DecorationStyle();

    @Data
    public static class TitleStyle {
        private String fontFamily = "微软雅黑";
        private int mainTitleFontSize = 20;
        private int subTitleFontSize = 14;
        private int sectionTitleFontSize = 16;
        private String mainTitleColor = "2c3e50";  // 深色标题
        private String subTitleColor = "6c757d";   // 灰色副标题
        private String sectionTitleColor = "2c3e50"; // 深色章节标题
        private int spacingBefore = 200;
        private int spacingAfter = 300;
        private boolean enableShadow = false;
        private String shadowColor = "e5e7eb";
        private boolean enableGradient = false;
        private String gradientStartColor = "f8f9ff";
        private String gradientEndColor = "f1f3ff";
        private boolean enableBorder = false;
        private String borderColor = "e8ecf4";
        private int borderWidth = 1;
    }

    @Data
    public static class TableStyle {
        private String fontFamily = "微软雅黑";
        private int fontSize = 12;
        private int headerFontSize = 13;
        private String labelColor = "6c757d";      // 标签灰色
        private String valueColor = "2c3e50";      // 值深色
        private String headerColor = "ffffff";     // 白色
        private String backgroundColor = "ffffff";  // 白色背景
        private String headerBackgroundColor = "565EFF"; // 主题色头部
        private String borderColor = "f1f3f4";     // 浅灰边框
        private String alternateRowColor = "ffffff"; // 交替行白色
        private int cellPadding = 120;
        private boolean enableBorder = true;
        private boolean enableAlternateRows = false;
        private boolean enableShadow = false;
        private boolean enableRoundedCorners = true;
        private int borderRadius = 6;
        private String shadowColor = "565EFF";
        private int shadowOpacity = 5; // 百分比
        // 信息卡片样式 - 基于图片样式
        private String[] cardColors = {"3b82f6", "10b981", "f59e0b", "ef4444", "8b5cf6", "06b6d4"};
        private boolean enableCardGradient = false;
        private String cardBackgroundColor = "f8f9ff";
        private int cardBorderRadius = 8;
        private boolean enableCardHover = false;
        // 新增卡片网格样式
        private boolean enableCardGrid = true;
        private int cardGridColumns = 2;
        private int cardGridGap = 150;
        private String cardBorderColor = "e8ecf4";
        private int cardBorderWidth = 1;
    }

    @Data
    public static class ChartStyle {
        private int defaultWidth = 400;
        private int defaultHeight = 300;
        private int columnWidth = 450;
        private int columnHeight = 320;
        private int lineWidth = 420;
        private int lineHeight = 300;
        private int pieWidth = 350;
        private int pieHeight = 350;
        private int spacingBefore = 200;
        private int spacingAfter = 300;
        private boolean enableTitle = true;
        private boolean enableBorder = false;
        private String borderColor = "e8ecf4";
        private boolean enableShadow = false;
        private String shadowColor = "565EFF";
        private int shadowOpacity = 5;
        private boolean enableRoundedCorners = false;
        private int borderRadius = 6;
        private String backgroundColor = "ffffff";
        private boolean enableGradientBackground = false;
        private String gradientStartColor = "f8f9ff";
        private String gradientEndColor = "f1f3ff";
        private int titleFontSize = 13;
        private String titleColor = "2c3e50";
        private String titleFontFamily = "微软雅黑";
        private boolean enableTitleIcon = true;
        private String titleIcon = "📊";
    }

    @Data
    public static class FactorStyle {
        private String fontFamily = "微软雅黑";
        private int topLevelFontSize = 14;
        private int parentGroupFontSize = 13;
        private int childFontSize = 12;
        private String titleColor = "2c3e50";      // 深色标题
        private String contentColor = "4b5563";    // 中灰内容
        private String accentColor = "565EFF";     // 强调色
        private String decorationColor = "565EFF"; // 装饰色
        private int indentUnit = 200;
        private int spacingBefore = 300;
        private int spacingAfter = 200;
        private boolean enableDecorations = true;
        private boolean enableBackground = false;
        private String backgroundColor = "ffffff";
        private boolean enableBorder = false;
        private String borderColor = "e8ecf4";
        private boolean enableShadow = false;
        private String shadowColor = "565EFF";
        private int shadowOpacity = 5;
        private boolean enableRoundedCorners = false;
        private int borderRadius = 6;
        private boolean enableGradient = false;
        private String gradientStartColor = "f8f9ff";
        private String gradientEndColor = "f1f3ff";
        private boolean enableHoverEffects = false;
        private String hoverShadowColor = "565EFF";
        private int hoverShadowOpacity = 10;
        private boolean enableLevelDecorations = true;
        private String levelDecoratorColor = "565EFF";
        private int levelDecoratorWidth = 2;
        private boolean enableFactorIcons = true;
        private String factorIcon = "▶";
        private String analysisIcon = "💡";
    }

    @Data
    public static class PageStyle {
        private String fontFamily = "微软雅黑";
        private boolean enableHeader = false;
        private boolean enableFooter = false;
        private boolean enablePageNumbers = false;
        private String headerText = "心理测评分析报告";
        private String footerText = "心理云平台生成";
        private String headerColor = "6b7280";
        private String footerColor = "9ca3af";
        private int headerFontSize = 10;
        private int footerFontSize = 9;
        private boolean enableWatermark = false;
        private String watermarkText = "CONFIDENTIAL";
        private String watermarkColor = "f3f4f6";
    }

    @Data
    public static class DecorationStyle {
        private boolean enableDividers = true;
        private String dividerColor = "e5e7eb";
        private int dividerHeight = 1;
        private boolean enableIcons = true;
        private String primaryIcon = "📊";
        private String analysisIcon = "💡";
        private String chartIcon = "📈";
        private String factorIcon = "▶";
        private String infoIcon = "ℹ";
        private String summaryIcon = "📋";
        private boolean enableGradients = false;
        private String gradientStartColor = "565EFF";
        private String gradientEndColor = "6c5ce7";
        private boolean enableAnimatedDividers = false;
        private String animatedDividerPattern = "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━";
        private boolean enableColorfulDividers = false;
        private String[] dividerColors = {"565EFF", "6c5ce7", "a855f7", "f093fb"};
        private boolean enableSectionDecorators = true;
        private String sectionDecoratorColor = "565EFF";
        private int sectionDecoratorWidth = 2;
        private boolean enableBackgroundPatterns = false;
        private String backgroundPatternColor = "f8f9ff";
        private int backgroundPatternOpacity = 30;
    }
}
