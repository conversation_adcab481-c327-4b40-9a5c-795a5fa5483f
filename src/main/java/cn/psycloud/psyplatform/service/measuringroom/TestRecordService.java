package cn.psycloud.psyplatform.service.measuringroom;

import cn.psycloud.psyplatform.dto.core.BSDatatableRes;
import cn.psycloud.psyplatform.dto.measuringroom.*;
import cn.psycloud.psyplatform.entity.measuringroom.TestRecordChartsEntity;
import cn.psycloud.psyplatform.entity.measuringroom.TestRecordEntity;
import cn.psycloud.psyplatform.entity.measuringroom.TestRecordExplainEntity;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 *  心理测评记录业务接口
 */
public interface TestRecordService {
    /**
     *  根据记录ID查询测评记录
     * @param recordId 记录id
     * @return 测评记录实体对象
     */
    TestRecordDto getById(Integer recordId);

    /**
     *  查询测评记录集合：分页
     * @param dto 测评记录实体对象
     * @return 测评记录集合
     */
    BSDatatableRes<TestRecordDto> getListByPaged(TestRecordDto dto);

    /**
     * 根据条件查询测评记录集合
     * @param dto 测评记录实体对象
     * @return 测评记录集合
     */
    List<TestRecordDto> getList(TestRecordDto dto);

    /**
     *  获取我的测评记录
     * @param dto 查询条件
     * @return 测评记录集合
     */
    BSDatatableRes<TestRecordDto> getMyRecords(TestRecordDto dto);

    /**
     *  保存测评记录
     * @param entity 测评记录实体对象
     * @return 测评记录id
     */
    int addRecord(TestRecordEntity entity);

    /**
     *  删除
     * @param id 记录id
     * @return 影响行数
     */
    int delete(Integer id);

    /**
     *  批量删除
     * @param ids id集合
     * @return 执行是否成功
     */
    boolean batchDel(String ids);

    /**
     *  更新测评状态
     * @param recordId 记录id
     * @param state 测评状态：0-未完成 1-已完成 2-测谎未通过
     * @return 影响行数
     */
    int updateTestState(Integer recordId, Integer state);

    /**
     *  更新测评开始时间
     * @param recordId 记录id
     * @return 影响行数
     */
    int updateTestStartTime(Integer recordId);

    /**
     *  验证个人信息是否符合量表要求
     * @param dto 测评记录实体对象
     * @return 是否符合
     */
    boolean isUserInfoComplete(TestRecordDto dto);

    /**
     *  判断是否异常
     * @param recordId 记录id
     * @return 异常标识
     */
    int isAbnormal(Integer recordId);

    /**
     *  查询九型人格测评记录集合：分页
     * @param dto 九型人格测评实体对象
     * @return 记录集合
     */
    BSDatatableRes<NineHouseStatDto> getNineHouseList(NineHouseStatDto dto);

    /**
     *  保存测评结果解释
     * @param entity 测评结果解释实体对象
     * @return 操作是否成功
     */
    boolean saveTestRecordExplain(TestRecordExplainEntity entity);

    /**
     * 获取因子结果解释
     * @param recordId 测评记录id
     * @return 因子结果解释列表
     */
    List<TestRecordExplainEntity> getFactorExplains(Integer recordId);

    /**
     *  保存测评报告里的图表
     * @param dto 测评报告图表实体对象
     * @return 操作是否成功
     */
    boolean saveTestRecordCharts(TestRecordChartsDto dto);

    /**
     *  保存测评报告里的图表（新版本，支持详细图表信息）
     * @param requestDto 测评报告图表请求实体对象
     * @return 操作是否成功
     */
    boolean saveTestRecordChartsV2(TestRecordChartsRequestDto requestDto);

    /**
     *  获取报告图表
     * @param recordId 测评记录id
     * @return 测评报告图表集合
     */
    List<TestRecordChartsEntity> getReportCharts(Integer recordId);

    /**
     *  导出测评记录
     * @param dto 查询条件
     * @return  测评记录集合
     */
    List<ExportTestRecordDto> getExportTestRecordList(TestRecordDto dto);

    /**
     *  测评报告导出word
     * @param response 响应
     * @param request 请求
     * @param recordId 测评记录Id
     * @return 文件名
     */
    String exportTestReportToWord(HttpServletResponse response, HttpServletRequest request, Integer recordId, String folderName);

    /**
     *  批量导出测评报告
     * @param response 响应
     * @param request 请求
     * @param dto 查询条件
     * @return 文件路径
     */
    String batchExportReport(HttpServletResponse response, HttpServletRequest request, TestRecordDto dto);

    /**
     * 获取因子结果解释（按父子关系组织）
     * @param recordId 测评记录id
     * @return 按父子关系组织的因子解释
     */
    List<FactorExplainHierarchyDto> getFactorExplainsWithHierarchy(Integer recordId);

    /**
     *  获取测评报告
     * @param recordId 测评记录id
     * @return 测评报告实体对象
     */
    ReportDto getReport(Integer recordId);
}
