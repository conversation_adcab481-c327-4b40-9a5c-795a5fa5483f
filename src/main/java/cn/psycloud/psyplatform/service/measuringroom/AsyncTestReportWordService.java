package cn.psycloud.psyplatform.service.measuringroom;

import java.util.concurrent.CompletableFuture;

/**
 * 异步测评报告Word生成服务接口
 */
public interface AsyncTestReportWordService {
    
    /**
     * 异步生成Word报告
     * @param recordId 测评记录ID
     * @param folderName 文件夹名称
     * @return CompletableFuture包装的文件路径
     */
    CompletableFuture<String> generateWordReportAsync(Integer recordId, String folderName);
    
    /**
     * 批量异步生成Word报告
     * @param recordIds 测评记录ID列表
     * @param folderName 文件夹名称
     * @return CompletableFuture包装的文件路径列表
     */
    CompletableFuture<java.util.List<String>> generateBatchWordReportsAsync(java.util.List<Integer> recordIds, String folderName);
    
    /**
     * 获取生成任务状态
     * @param taskId 任务ID
     * @return 任务状态
     */
    String getTaskStatus(String taskId);
}
