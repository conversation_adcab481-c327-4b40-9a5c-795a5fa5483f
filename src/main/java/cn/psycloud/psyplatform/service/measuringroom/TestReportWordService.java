package cn.psycloud.psyplatform.service.measuringroom;

import cn.psycloud.psyplatform.dto.measuringroom.ReportDto;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 测评报告Word生成服务接口
 */
public interface TestReportWordService {
    
    /**
     * 生成与网页一致的Word报告
     * @param recordId 测评记录ID
     * @param folderName 文件夹名称
     * @return 生成的文件路径
     */
    String generateWordReport(Integer recordId, String folderName);
    
    /**
     * 生成Word报告并直接下载
     * @param response HTTP响应
     * @param request HTTP请求
     * @param recordId 测评记录ID
     * @return 生成的文件路径
     */
    String generateAndDownloadWordReport(HttpServletResponse response, HttpServletRequest request, Integer recordId);
    
    /**
     * 生成测试Word模板（用于预览新样式）
     * @return 生成的文件路径
     */
    String generateTestWordTemplate();
}
