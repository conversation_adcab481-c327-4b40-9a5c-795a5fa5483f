package cn.psycloud.psyplatform.service.measuringroom;

/**
 * Word报告缓存服务接口
 */
public interface WordReportCacheService {
    
    /**
     * 获取缓存的报告路径
     * @param recordId 测评记录ID
     * @return 报告文件路径，如果不存在返回null
     */
    String getCachedReportPath(Integer recordId);
    
    /**
     * 缓存报告路径
     * @param recordId 测评记录ID
     * @param filePath 报告文件路径
     */
    void cacheReportPath(Integer recordId, String filePath);
    
    /**
     * 检查报告是否需要重新生成
     * @param recordId 测评记录ID
     * @return true表示需要重新生成，false表示可以使用缓存
     */
    boolean needRegenerate(Integer recordId);
    
    /**
     * 清除指定记录的缓存
     * @param recordId 测评记录ID
     */
    void clearCache(Integer recordId);
    
    /**
     * 清除所有缓存
     */
    void clearAllCache();
    
    /**
     * 获取缓存统计信息
     * @return 缓存统计信息
     */
    CacheStats getCacheStats();
    
    /**
     * 缓存统计信息
     */
    class CacheStats {
        private long hitCount;
        private long missCount;
        private long cacheSize;
        
        public CacheStats(long hitCount, long missCount, long cacheSize) {
            this.hitCount = hitCount;
            this.missCount = missCount;
            this.cacheSize = cacheSize;
        }
        
        public long getHitCount() { return hitCount; }
        public long getMissCount() { return missCount; }
        public long getCacheSize() { return cacheSize; }
        public double getHitRate() { 
            long total = hitCount + missCount;
            return total == 0 ? 0.0 : (double) hitCount / total; 
        }
    }
}
