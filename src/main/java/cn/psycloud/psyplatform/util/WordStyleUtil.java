package cn.psycloud.psyplatform.util;

import cn.psycloud.psyplatform.config.WordTemplateConfig;
import org.apache.poi.xwpf.usermodel.*;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.*;

import java.math.BigInteger;

/**
 * Word样式工具类 - 美观版本
 * 提供丰富的样式设置方法，生成美观的Word文档
 */
public class WordStyleUtil {
    
    /**
     * 应用主标题样式
     */
    public static void applyMainTitleStyle(XWPFRun run, WordTemplateConfig.TitleStyle style) {
        run.setFontFamily(style.getFontFamily());
        run.setFontSize(style.getMainTitleFontSize());
        run.setColor(style.getMainTitleColor());
        run.setBold(true);
    }

    /**
     * 应用副标题样式
     */
    public static void applySubTitleStyle(XWPFRun run, WordTemplateConfig.TitleStyle style) {
        run.setFontFamily(style.getFontFamily());
        run.setFontSize(style.getSubTitleFontSize());
        run.setColor(style.getSubTitleColor());
        run.setItalic(true);
    }

    /**
     * 应用标题样式（兼容性方法）
     */
    public static void applyTitleStyle(XWPFRun run, WordTemplateConfig.TitleStyle style) {
        applyMainTitleStyle(run, style);
    }

    /**
     * 应用章节标题样式
     */
    public static void applySectionTitleStyle(XWPFRun run, WordTemplateConfig.TitleStyle style) {
        run.setFontFamily(style.getFontFamily());
        run.setFontSize(style.getSectionTitleFontSize());
        run.setColor(style.getSectionTitleColor());
        run.setBold(true);
    }
    
    /**
     * 应用段落间距
     */
    public static void applyParagraphSpacing(XWPFParagraph paragraph, int spacingBefore, int spacingAfter) {
        paragraph.setSpacingBefore(spacingBefore);
        paragraph.setSpacingAfter(spacingAfter);
    }
    
    /**
     * 应用因子标题样式
     */
    public static void applyFactorTitleStyle(XWPFRun run, WordTemplateConfig.FactorStyle style, int level) {
        run.setFontFamily(style.getFontFamily());
        run.setColor(style.getTitleColor());
        run.setBold(true);
        
        // 根据层级设置字体大小
        switch (level) {
            case 1:
                run.setFontSize(style.getTopLevelFontSize());
                break;
            case 2:
                run.setFontSize(style.getParentGroupFontSize());
                break;
            default:
                run.setFontSize(style.getChildFontSize());
                break;
        }
    }
    
    /**
     * 应用因子内容样式
     */
    public static void applyFactorContentStyle(XWPFRun run, WordTemplateConfig.FactorStyle style) {
        run.setFontFamily(style.getFontFamily());
        run.setFontSize(style.getChildFontSize());
        run.setColor(style.getContentColor());
    }
    
    /**
     * 应用表格样式（兼容性方法）
     */
    public static void applyTableStyle(XWPFTable table, WordTemplateConfig.TableStyle style) {
        applyEnhancedTableStyle(table, style);
    }

    /**
     * 应用增强表格样式（简化版）
     */
    public static void applyEnhancedTableStyle(XWPFTable table, WordTemplateConfig.TableStyle style) {
        // 设置表格宽度
        CTTblPr tblPr = table.getCTTbl().getTblPr();
        if (tblPr == null) {
            tblPr = table.getCTTbl().addNewTblPr();
        }

        CTTblWidth tblWidth = tblPr.getTblW();
        if (tblWidth == null) {
            tblWidth = tblPr.addNewTblW();
        }
        tblWidth.setType(STTblWidth.DXA);
        tblWidth.setW(BigInteger.valueOf(9500));

        // 设置单元格样式
        for (int i = 0; i < table.getRows().size(); i++) {
            XWPFTableRow row = table.getRow(i);
            for (int j = 0; j < row.getTableCells().size(); j++) {
                XWPFTableCell cell = row.getCell(j);

                // 设置单元格垂直对齐
                cell.setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);

                // 设置背景色
                cell.setColor(style.getBackgroundColor());
            }
        }
    }


    
    /**
     * 应用表格单元格样式
     */
    public static void applyTableCellStyle(XWPFTableCell cell, WordTemplateConfig.TableStyle style, boolean isLabel) {
        XWPFParagraph paragraph = cell.getParagraphs().get(0);
        paragraph.setAlignment(ParagraphAlignment.LEFT);
        paragraph.setSpacingBefore(style.getCellPadding());
        paragraph.setSpacingAfter(style.getCellPadding());
        
        XWPFRun run = paragraph.getRuns().size() > 0 ? paragraph.getRuns().get(0) : paragraph.createRun();
        
        run.setFontFamily(style.getFontFamily());
        run.setFontSize(style.getFontSize());
        
        if (isLabel) {
            run.setBold(true);
            run.setColor(style.getLabelColor());
        } else {
            run.setColor(style.getValueColor());
        }
    }
    
    /**
     * 获取图表尺寸
     */
    public static ChartSize getChartSize(String chartType, WordTemplateConfig.ChartStyle style) {
        switch (chartType) {
            case "column":
            case "bar":
                return new ChartSize(style.getColumnWidth(), style.getColumnHeight());
            case "line":
                return new ChartSize(style.getLineWidth(), style.getLineHeight());
            case "pie":
                return new ChartSize(style.getPieWidth(), style.getPieHeight());
            default:
                return new ChartSize(style.getDefaultWidth(), style.getDefaultHeight());
        }
    }
    
    /**
     * 添加增强的装饰性分割线
     */
    public static void addDecorativeDivider(XWPFDocument document, WordTemplateConfig.DecorationStyle style) {
        if (!style.isEnableDividers()) return;

        XWPFParagraph divider = document.createParagraph();
        divider.setAlignment(ParagraphAlignment.CENTER);
        divider.setSpacingBefore(300);
        divider.setSpacingAfter(300);

        if (style.isEnableColorfulDividers() && style.getDividerColors() != null && style.getDividerColors().length > 0) {
            // 创建彩色渐变分割线效果
            for (int i = 0; i < style.getDividerColors().length; i++) {
                XWPFRun dividerRun = divider.createRun();
                String pattern = "━━━━━━━━━━━━━";
                dividerRun.setText(pattern);
                dividerRun.setColor(style.getDividerColors()[i]);
                dividerRun.setFontSize(style.getDividerHeight() + 6);
                if (i < style.getDividerColors().length - 1) {
                    dividerRun.addBreak();
                }
            }
        } else {
            XWPFRun dividerRun = divider.createRun();
            dividerRun.setText(style.getAnimatedDividerPattern());
            dividerRun.setColor(style.getDividerColor());
            dividerRun.setFontSize(style.getDividerHeight() + 6);
        }
    }

    /**
     * 添加带图标的章节标题
     */
    public static void addIconSectionTitle(XWPFDocument document, String icon, String title,
                                         WordTemplateConfig.TitleStyle titleStyle,
                                         WordTemplateConfig.DecorationStyle decorationStyle) {
        XWPFParagraph sectionTitle = document.createParagraph();
        sectionTitle.setSpacingBefore(titleStyle.getSpacingBefore());
        sectionTitle.setSpacingAfter(titleStyle.getSpacingAfter());

        if (decorationStyle.isEnableIcons()) {
            XWPFRun iconRun = sectionTitle.createRun();
            iconRun.setText(icon + " ");
            iconRun.setFontSize(titleStyle.getSectionTitleFontSize());
        }

        XWPFRun titleRun = sectionTitle.createRun();
        titleRun.setText(title);
        applySectionTitleStyle(titleRun, titleStyle);
    }

    /**
     * 创建增强的信息卡片样式
     */
    public static void createInfoCard(XWPFTableCell cell, String label, String value,
                                    String accentColor, WordTemplateConfig.TableStyle style) {
        // 设置单元格样式
        cell.setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);
        cell.setColor(style.getCardBackgroundColor());

        // 清除默认段落
        if (!cell.getParagraphs().isEmpty()) {
            cell.removeParagraph(0);
        }

        // 添加装饰性顶部边框效果（通过文本模拟）
        XWPFParagraph borderPara = cell.addParagraph();
        borderPara.setAlignment(ParagraphAlignment.CENTER);
        borderPara.setSpacingAfter(100);

        XWPFRun borderRun = borderPara.createRun();
        borderRun.setText("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
        borderRun.setColor(accentColor);
        borderRun.setFontSize(6);

        // 添加标签
        XWPFParagraph labelPara = cell.addParagraph();
        labelPara.setAlignment(ParagraphAlignment.CENTER);
        labelPara.setSpacingAfter(120);

        XWPFRun labelRun = labelPara.createRun();
        labelRun.setText(label.toUpperCase());
        labelRun.setFontFamily(style.getFontFamily());
        labelRun.setFontSize(11);
        labelRun.setColor(style.getLabelColor());
        labelRun.setBold(true);

        // 添加值
        XWPFParagraph valuePara = cell.addParagraph();
        valuePara.setAlignment(ParagraphAlignment.CENTER);
        valuePara.setSpacingAfter(100);

        XWPFRun valueRun = valuePara.createRun();
        valueRun.setText(value);
        valueRun.setFontFamily(style.getFontFamily());
        valueRun.setFontSize(16);
        valueRun.setBold(true);
        valueRun.setColor(accentColor);

        // 添加装饰性底部间距
        XWPFParagraph spacerPara = cell.addParagraph();
        spacerPara.setSpacingAfter(50);
    }

    /**
     * 添加页眉（简化版）
     */
    public static void addHeader(XWPFDocument document, WordTemplateConfig.PageStyle pageStyle) {
        if (!pageStyle.isEnableHeader()) return;

        // 简化实现：在文档开头添加页眉样式的段落
        XWPFParagraph headerPara = document.createParagraph();
        headerPara.setAlignment(ParagraphAlignment.CENTER);
        headerPara.setSpacingAfter(400);

        XWPFRun headerRun = headerPara.createRun();
        headerRun.setText(pageStyle.getHeaderText());
        headerRun.setFontFamily(pageStyle.getFontFamily());
        headerRun.setFontSize(pageStyle.getHeaderFontSize());
        headerRun.setColor(pageStyle.getHeaderColor());

        // 添加分割线
        XWPFParagraph dividerPara = document.createParagraph();
        dividerPara.setAlignment(ParagraphAlignment.CENTER);
        dividerPara.setSpacingAfter(200);
        XWPFRun dividerRun = dividerPara.createRun();
        dividerRun.setText("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
        dividerRun.setColor("e5e7eb");
        dividerRun.setFontSize(8);
    }

    /**
     * 添加页脚（简化版）
     */
    public static void addFooter(XWPFDocument document, WordTemplateConfig.PageStyle pageStyle) {
        if (!pageStyle.isEnableFooter()) return;

        // 简化实现：在文档末尾添加页脚样式的段落
        XWPFParagraph dividerPara = document.createParagraph();
        dividerPara.setAlignment(ParagraphAlignment.CENTER);
        dividerPara.setSpacingBefore(400);
        XWPFRun dividerRun = dividerPara.createRun();
        dividerRun.setText("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
        dividerRun.setColor("e5e7eb");
        dividerRun.setFontSize(8);

        XWPFParagraph footerPara = document.createParagraph();
        footerPara.setAlignment(ParagraphAlignment.CENTER);
        footerPara.setSpacingBefore(200);

        XWPFRun footerRun = footerPara.createRun();
        footerRun.setText(pageStyle.getFooterText());
        footerRun.setFontFamily(pageStyle.getFontFamily());
        footerRun.setFontSize(pageStyle.getFooterFontSize());
        footerRun.setColor(pageStyle.getFooterColor());
    }

    /**
     * 创建美观的报告标题区域
     */
    public static void createBeautifulReportTitle(XWPFDocument document, String scaleName,
                                                WordTemplateConfig.TitleStyle titleStyle,
                                                WordTemplateConfig.DecorationStyle decorationStyle) {
        // 添加顶部装饰线
        addGradientDivider(document, decorationStyle);

        // 主标题容器
        XWPFParagraph titleContainer = document.createParagraph();
        titleContainer.setAlignment(ParagraphAlignment.CENTER);
        titleContainer.setSpacingBefore(600);
        titleContainer.setSpacingAfter(300);

        // 添加装饰图标
        if (decorationStyle.isEnableIcons()) {
            XWPFRun iconRun = titleContainer.createRun();
            iconRun.setText("🎯 ");
            iconRun.setFontSize(32);
        }

        // 主标题
        XWPFRun mainTitleRun = titleContainer.createRun();
        mainTitleRun.setText("《" + scaleName + "》");
        mainTitleRun.setFontFamily(titleStyle.getFontFamily());
        mainTitleRun.setFontSize(titleStyle.getMainTitleFontSize());
        mainTitleRun.setColor(titleStyle.getMainTitleColor());
        mainTitleRun.setBold(true);

        // 副标题
        XWPFParagraph subtitlePara = document.createParagraph();
        subtitlePara.setAlignment(ParagraphAlignment.CENTER);
        subtitlePara.setSpacingAfter(200);

        XWPFRun subtitleRun = subtitlePara.createRun();
        subtitleRun.setText("心理测评分析报告");
        subtitleRun.setFontFamily(titleStyle.getFontFamily());
        subtitleRun.setFontSize(titleStyle.getSubTitleFontSize());
        subtitleRun.setColor(titleStyle.getSubTitleColor());
        subtitleRun.setItalic(true);

        // 生成时间
        XWPFParagraph timePara = document.createParagraph();
        timePara.setAlignment(ParagraphAlignment.CENTER);
        timePara.setSpacingAfter(400);

        XWPFRun timeRun = timePara.createRun();
        timeRun.setText("📅 生成时间：" + new java.text.SimpleDateFormat("yyyy年MM月dd日 HH:mm").format(new java.util.Date()));
        timeRun.setFontFamily(titleStyle.getFontFamily());
        timeRun.setFontSize(12);
        timeRun.setColor("9ca3af");

        // 底部装饰线
        addGradientDivider(document, decorationStyle);
    }

    /**
     * 创建渐变装饰分割线
     */
    public static void addGradientDivider(XWPFDocument document, WordTemplateConfig.DecorationStyle style) {
        XWPFParagraph divider = document.createParagraph();
        divider.setAlignment(ParagraphAlignment.CENTER);
        divider.setSpacingBefore(200);
        divider.setSpacingAfter(200);

        // 创建渐变效果的分割线
        String[] colors = {"727cf5", "8b5cf6", "a855f7", "c084fc", "d8b4fe"};
        for (int i = 0; i < colors.length; i++) {
            XWPFRun run = divider.createRun();
            run.setText("●●●●●●●●●●");
            run.setColor(colors[i]);
            run.setFontSize(8);
            if (i < colors.length - 1) {
                run.addTab();
            }
        }
    }

    /**
     * 创建美观的基本信息卡片网格
     */
    public static void createInfoCardGrid(XWPFDocument document, String[][] infoData,
                                        WordTemplateConfig.TableStyle tableStyle,
                                        WordTemplateConfig.DecorationStyle decorationStyle) {
        if (document == null || infoData == null || infoData.length == 0) {
            return;
        }

        // 章节标题
        createSectionTitle(document, "📊", "基本信息", tableStyle, decorationStyle);

        try {
            // 创建3列2行的表格
            XWPFTable table = document.createTable(2, 3);

            // 设置表格宽度
            CTTblPr tblPr = table.getCTTbl().getTblPr();
            if (tblPr == null) {
                tblPr = table.getCTTbl().addNewTblPr();
            }
            CTTblWidth tblWidth = tblPr.getTblW();
            if (tblWidth == null) {
                tblWidth = tblPr.addNewTblW();
            }
            tblWidth.setType(STTblWidth.PCT);
            tblWidth.setW(BigInteger.valueOf(5000)); // 100%

            // 设置列宽
            for (int i = 0; i < 3; i++) {
                CTTblGrid tblGrid = table.getCTTbl().getTblGrid();
                if (tblGrid == null) {
                    tblGrid = table.getCTTbl().addNewTblGrid();
                }
                CTTblGridCol gridCol = tblGrid.addNewGridCol();
                gridCol.setW(BigInteger.valueOf(3000));
            }

            // 填充数据
            String[] colors = tableStyle != null && tableStyle.getCardColors() != null ?
                tableStyle.getCardColors() : new String[]{"3b82f6", "10b981", "f59e0b", "ef4444", "8b5cf6", "06b6d4"};
            int colorIndex = 0;

            for (int row = 0; row < 2; row++) {
                for (int col = 0; col < 3; col++) {
                    int dataIndex = row * 3 + col;
                    if (dataIndex < infoData.length && infoData[dataIndex] != null && infoData[dataIndex].length >= 2) {
                        String[] cardData = infoData[dataIndex];
                        String color = colors[colorIndex % colors.length];
                        createEnhancedInfoCard(table.getRow(row).getCell(col),
                                             cardData[0], cardData[1], color, tableStyle);
                        colorIndex++;
                    }
                }
            }

            // 添加表格间距
            document.createParagraph().setSpacingAfter(600);

        } catch (Exception e) {
            // 如果表格创建失败，添加简单的文本信息
            XWPFParagraph errorPara = document.createParagraph();
            XWPFRun errorRun = errorPara.createRun();
            errorRun.setText("基本信息表格创建失败");
            errorRun.setColor("ef4444");
        }
    }

    /**
     * 创建增强的信息卡片
     */
    public static void createEnhancedInfoCard(XWPFTableCell cell, String label, String value,
                                            String accentColor, WordTemplateConfig.TableStyle style) {
        if (cell == null || label == null || value == null) {
            return;
        }

        try {
            // 设置单元格样式
            cell.setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);
            cell.setColor("ffffff");

            // 设置单元格边距
            CTTcPr tcPr = cell.getCTTc().getTcPr();
            if (tcPr == null) {
                tcPr = cell.getCTTc().addNewTcPr();
            }
            CTTblWidth cellWidth = tcPr.getTcW();
            if (cellWidth == null) {
                cellWidth = tcPr.addNewTcW();
            }
            cellWidth.setType(STTblWidth.PCT);
            cellWidth.setW(BigInteger.valueOf(3333)); // 33.33%

            // 清除默认段落
            if (!cell.getParagraphs().isEmpty()) {
                cell.removeParagraph(0);
            }

            // 顶部装饰条
            XWPFParagraph topBar = cell.addParagraph();
            topBar.setAlignment(ParagraphAlignment.CENTER);
            topBar.setSpacingAfter(150);

            XWPFRun barRun = topBar.createRun();
            barRun.setText("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
            barRun.setColor(accentColor != null ? accentColor : "727cf5");
            barRun.setFontSize(4);

            // 标签
            XWPFParagraph labelPara = cell.addParagraph();
            labelPara.setAlignment(ParagraphAlignment.CENTER);
            labelPara.setSpacingAfter(100);

            XWPFRun labelRun = labelPara.createRun();
            labelRun.setText(label);
            String fontFamily = style != null ? style.getFontFamily() : "微软雅黑";
            labelRun.setFontFamily(fontFamily);
            labelRun.setFontSize(11);
            labelRun.setColor("6b7280");
            labelRun.setBold(true);

            // 值
            XWPFParagraph valuePara = cell.addParagraph();
            valuePara.setAlignment(ParagraphAlignment.CENTER);
            valuePara.setSpacingAfter(150);

            XWPFRun valueRun = valuePara.createRun();
            valueRun.setText(value);
            valueRun.setFontFamily(fontFamily);
            valueRun.setFontSize(15);
            valueRun.setBold(true);
            valueRun.setColor(accentColor != null ? accentColor : "727cf5");

            // 底部装饰
            XWPFParagraph bottomDecor = cell.addParagraph();
            bottomDecor.setAlignment(ParagraphAlignment.CENTER);

            XWPFRun decorRun = bottomDecor.createRun();
            decorRun.setText("◆");
            decorRun.setColor(accentColor != null ? accentColor : "727cf5");
            decorRun.setFontSize(8);

        } catch (Exception e) {
            // 如果卡片创建失败，添加简单文本
            try {
                XWPFParagraph simplePara = cell.addParagraph();
                XWPFRun simpleRun = simplePara.createRun();
                simpleRun.setText(label + ": " + value);
                simpleRun.setFontFamily("微软雅黑");
                simpleRun.setFontSize(12);
            } catch (Exception ignored) {
                // 忽略二次异常
            }
        }
    }

    /**
     * 创建美观的章节标题
     */
    public static void createSectionTitle(XWPFDocument document, String icon, String title,
                                        WordTemplateConfig.TableStyle tableStyle,
                                        WordTemplateConfig.DecorationStyle decorationStyle) {
        if (document == null || title == null || title.trim().isEmpty()) {
            return;
        }

        XWPFParagraph titlePara = document.createParagraph();
        titlePara.setSpacingBefore(600);
        titlePara.setSpacingAfter(400);

        // 图标
        if (decorationStyle != null && decorationStyle.isEnableIcons() && icon != null) {
            XWPFRun iconRun = titlePara.createRun();
            iconRun.setText(icon + " ");
            iconRun.setFontSize(18);
        }

        // 标题文字
        XWPFRun titleRun = titlePara.createRun();
        titleRun.setText(title);

        // 安全获取字体配置
        String fontFamily = tableStyle != null ? tableStyle.getFontFamily() : "微软雅黑";
        titleRun.setFontFamily(fontFamily);
        titleRun.setFontSize(18);
        titleRun.setColor("2c3e50");
        titleRun.setBold(true);

        // 装饰线
        XWPFParagraph decorLine = document.createParagraph();
        decorLine.setSpacingAfter(300);

        XWPFRun lineRun = decorLine.createRun();
        lineRun.setText("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");

        // 安全获取装饰色
        String decorColor = decorationStyle != null ? decorationStyle.getGradientStartColor() : "727cf5";
        lineRun.setColor(decorColor);
        lineRun.setFontSize(6);
    }

    /**
     * 创建美观的因子层级区域
     */
    public static void createFactorLevelSection(XWPFDocument document, String factorName, String interpretation,
                                              WordTemplateConfig.FactorStyle factorStyle,
                                              WordTemplateConfig.DecorationStyle decorationStyle,
                                              int level) {
        // 因子标题容器
        XWPFParagraph factorTitle = document.createParagraph();
        factorTitle.setSpacingBefore(factorStyle.getSpacingBefore());
        factorTitle.setSpacingAfter(200);

        // 根据层级设置缩进
        int indent = (level - 1) * factorStyle.getIndentUnit();
        factorTitle.setIndentationLeft(indent);

        // 层级装饰符号
        XWPFRun levelRun = factorTitle.createRun();
        String levelSymbol = level == 1 ? "🔹 " : level == 2 ? "▶ " : "• ";
        levelRun.setText(levelSymbol);
        levelRun.setFontSize(getFontSizeByLevel(level, factorStyle));
        levelRun.setColor(factorStyle.getAccentColor());

        // 因子名称
        XWPFRun nameRun = factorTitle.createRun();
        nameRun.setText(factorName);
        nameRun.setFontFamily(factorStyle.getFontFamily());
        nameRun.setFontSize(getFontSizeByLevel(level, factorStyle));
        nameRun.setColor(factorStyle.getTitleColor());
        nameRun.setBold(true);

        // 因子解释
        if (interpretation != null && !interpretation.trim().isEmpty()) {
            XWPFParagraph interpretationPara = document.createParagraph();
            interpretationPara.setSpacingAfter(factorStyle.getSpacingAfter());
            interpretationPara.setIndentationLeft(indent + 300);
            interpretationPara.setIndentationRight(200);

            // 背景效果模拟（通过缩进和边框）
            if (factorStyle.isEnableBackground()) {
                interpretationPara.setIndentationLeft(indent + 400);
            }

            XWPFRun interpretationRun = interpretationPara.createRun();
            interpretationRun.setText(interpretation);
            interpretationRun.setFontFamily(factorStyle.getFontFamily());
            interpretationRun.setFontSize(factorStyle.getChildFontSize());
            interpretationRun.setColor(factorStyle.getContentColor());
        }
    }

    /**
     * 根据层级获取字体大小
     */
    private static int getFontSizeByLevel(int level, WordTemplateConfig.FactorStyle style) {
        switch (level) {
            case 1: return style.getTopLevelFontSize();
            case 2: return style.getParentGroupFontSize();
            default: return style.getChildFontSize();
        }
    }

    /**
     * 创建美观的图表容器
     */
    public static void createChartContainer(XWPFDocument document, String chartTitle,
                                          WordTemplateConfig.ChartStyle chartStyle,
                                          WordTemplateConfig.DecorationStyle decorationStyle) {
        // 图表标题
        XWPFParagraph titlePara = document.createParagraph();
        titlePara.setAlignment(ParagraphAlignment.CENTER);
        titlePara.setSpacingBefore(chartStyle.getSpacingBefore());
        titlePara.setSpacingAfter(200);

        if (decorationStyle.isEnableIcons()) {
            XWPFRun iconRun = titlePara.createRun();
            iconRun.setText(chartStyle.getTitleIcon() + " ");
            iconRun.setFontSize(chartStyle.getTitleFontSize());
            iconRun.setColor(chartStyle.getTitleColor());
        }

        XWPFRun titleRun = titlePara.createRun();
        titleRun.setText(chartTitle);
        titleRun.setFontFamily(chartStyle.getTitleFontFamily());
        titleRun.setFontSize(chartStyle.getTitleFontSize());
        titleRun.setColor(chartStyle.getTitleColor());
        titleRun.setBold(true);

        // 装饰性边框
        XWPFParagraph borderPara = document.createParagraph();
        borderPara.setAlignment(ParagraphAlignment.CENTER);
        borderPara.setSpacingAfter(100);

        XWPFRun borderRun = borderPara.createRun();
        borderRun.setText("┌─────────────────────────────────────────────────────────────────┐");
        borderRun.setColor(chartStyle.getBorderColor());
        borderRun.setFontSize(8);
    }

    /**
     * 创建美观的报告总结区域
     */
    public static void createReportSummary(XWPFDocument document, String scaleName,
                                         WordTemplateConfig.TitleStyle titleStyle,
                                         WordTemplateConfig.DecorationStyle decorationStyle) {
        // 分割线
        addGradientDivider(document, decorationStyle);

        // 总结标题
        createSectionTitle(document, "📋", "报告总结", null, decorationStyle);

        // 总结内容容器
        XWPFParagraph summaryPara = document.createParagraph();
        summaryPara.setSpacingBefore(300);
        summaryPara.setSpacingAfter(400);
        summaryPara.setIndentationLeft(400);
        summaryPara.setIndentationRight(400);

        XWPFRun summaryRun = summaryPara.createRun();
        summaryRun.setText("本报告基于《" + scaleName + "》测评结果生成，详细分析了被测者在各个维度的表现。" +
                          "建议结合具体情况进行综合评估，如需进一步了解，请咨询专业心理咨询师。");
        summaryRun.setFontFamily(titleStyle.getFontFamily());
        summaryRun.setFontSize(13);
        summaryRun.setColor("4b5563");

        // 免责声明
        XWPFParagraph disclaimerPara = document.createParagraph();
        disclaimerPara.setAlignment(ParagraphAlignment.CENTER);
        disclaimerPara.setSpacingBefore(300);
        disclaimerPara.setSpacingAfter(600);

        XWPFRun disclaimerRun = disclaimerPara.createRun();
        disclaimerRun.setText("⚠ 本报告仅供参考，不能替代专业心理诊断 ⚠");
        disclaimerRun.setFontFamily(titleStyle.getFontFamily());
        disclaimerRun.setFontSize(11);
        disclaimerRun.setColor("f59e0b");
        disclaimerRun.setItalic(true);
        disclaimerRun.setBold(true);

        // 底部装饰
        addGradientDivider(document, decorationStyle);
    }

    /**
     * 图表尺寸类
     */
    public static class ChartSize {
        private final int width;
        private final int height;

        public ChartSize(int width, int height) {
            this.width = width;
            this.height = height;
        }

        public int getWidth() {
            return width;
        }

        public int getHeight() {
            return height;
        }
    }

    /**
     * 创建符合图片样式的基本信息卡片网格
     */
    public static void createModernInfoCardGrid(XWPFDocument document, String[][] infoData,
                                            WordTemplateConfig.TableStyle tableStyle,
                                            WordTemplateConfig.DecorationStyle decorationStyle) {
        if (infoData == null || infoData.length == 0) {
            return;
        }

        // 创建章节标题
        createModernSectionTitle(document, "ℹ", "基本信息", tableStyle, decorationStyle);

        // 创建卡片网格表格 - 3列布局
        int rows = (int) Math.ceil(infoData.length / 3.0);
        XWPFTable cardTable = document.createTable(rows, 3);
        
        // 设置表格样式
        cardTable.setWidth("100%");
        cardTable.removeBorders();
        
        // 填充卡片数据
        for (int i = 0; i < infoData.length; i++) {
            int row = i / 3;
            int col = i % 3;
            
            if (row < cardTable.getRows().size() && col < cardTable.getRow(row).getTableCells().size()) {
                XWPFTableCell cell = cardTable.getRow(row).getCell(col);
                createModernInfoCard(cell, infoData[i][0], infoData[i][1], tableStyle);
            }
        }

        // 添加间距
        XWPFParagraph spacing = document.createParagraph();
        spacing.setSpacingAfter(300);
    }

    /**
     * 创建现代风格的信息卡片
     */
    public static void createModernInfoCard(XWPFTableCell cell, String label, String value, 
                                        WordTemplateConfig.TableStyle style) {
        // 清除默认内容
        cell.removeParagraph(0);
        
        // 设置单元格背景色
        cell.setColor("f8f9ff");
        
        // 创建标签段落
        XWPFParagraph labelPara = cell.addParagraph();
        labelPara.setSpacingBefore(80);
        labelPara.setSpacingAfter(40);
        labelPara.setAlignment(ParagraphAlignment.LEFT);
        
        XWPFRun labelRun = labelPara.createRun();
        labelRun.setText(label);
        labelRun.setFontFamily("微软雅黑");
        labelRun.setFontSize(10);
        labelRun.setColor("6c757d");
        labelRun.setBold(false);
        
        // 创建值段落
        XWPFParagraph valuePara = cell.addParagraph();
        valuePara.setSpacingBefore(0);
        valuePara.setSpacingAfter(80);
        valuePara.setAlignment(ParagraphAlignment.LEFT);
        
        XWPFRun valueRun = valuePara.createRun();
        valueRun.setText(value != null ? value : "");
        valueRun.setFontFamily("微软雅黑");
        valueRun.setFontSize(12);
        valueRun.setColor("2c3e50");
        valueRun.setBold(true);
        
        // 设置单元格样式
        cell.setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);
        
        // 添加边框 - 使用段落边框
        XWPFParagraph borderPara = cell.addParagraph();
        borderPara.setBorderTop(Borders.SINGLE);
        borderPara.setBorderBottom(Borders.SINGLE);
        borderPara.setBorderLeft(Borders.SINGLE);
        borderPara.setBorderRight(Borders.SINGLE);
    }

    /**
     * 创建现代风格的章节标题
     */
    public static void createModernSectionTitle(XWPFDocument document, String icon, String title,
                                           WordTemplateConfig.TableStyle tableStyle,
                                           WordTemplateConfig.DecorationStyle decorationStyle) {
        XWPFParagraph paragraph = document.createParagraph();
        paragraph.setSpacingBefore(300);
        paragraph.setSpacingAfter(150);
        paragraph.setAlignment(ParagraphAlignment.LEFT);
        
        XWPFRun run = paragraph.createRun();
        if (icon != null) {
            run.setText(icon + " " + title);
        } else {
            run.setText(title);
        }
        
        run.setFontFamily("微软雅黑");
        run.setFontSize(14);
        run.setColor("2c3e50");
        run.setBold(true);
    }

    /**
     * 创建现代风格的因子分析区域
     */
    public static void createModernFactorSection(XWPFDocument document, String factorName, String interpretation,
                                            WordTemplateConfig.FactorStyle factorStyle,
                                            WordTemplateConfig.DecorationStyle decorationStyle,
                                            int level) {
        XWPFParagraph paragraph = document.createParagraph();
        paragraph.setSpacingBefore(300);
        paragraph.setSpacingAfter(200);
        
        // 根据层级设置缩进
        if (level > 1) {
            paragraph.setIndentationLeft(level * 300);
        }
        
        XWPFRun run = paragraph.createRun();
        String icon = "▶ ";
        run.setText(icon + factorName);
        
        run.setFontFamily("微软雅黑");
        run.setFontSize(getFontSizeByLevel(level, factorStyle));
        run.setColor("2c3e50");
        run.setBold(true);
        
        // 添加解释内容
        if (interpretation != null && !interpretation.trim().isEmpty()) {
            XWPFParagraph contentPara = document.createParagraph();
            contentPara.setSpacingBefore(100);
            contentPara.setSpacingAfter(200);
            
            if (level > 1) {
                contentPara.setIndentationLeft(level * 300);
            }
            
            XWPFRun contentRun = contentPara.createRun();
            contentRun.setText(interpretation);
            contentRun.setFontFamily("微软雅黑");
            contentRun.setFontSize(13);
            contentRun.setColor("4b5563");
            contentRun.setBold(false);
        }
    }

    /**
     * 创建现代风格的图表容器
     */
    public static void createModernChartContainer(XWPFDocument document, String chartTitle,
                                             WordTemplateConfig.ChartStyle chartStyle,
                                             WordTemplateConfig.DecorationStyle decorationStyle) {
        XWPFParagraph titlePara = document.createParagraph();
        titlePara.setSpacingBefore(400);
        titlePara.setSpacingAfter(200);
        titlePara.setAlignment(ParagraphAlignment.CENTER);
        
        XWPFRun titleRun = titlePara.createRun();
        String icon = "📊 ";
        titleRun.setText(icon + chartTitle);
        
        titleRun.setFontFamily("微软雅黑");
        titleRun.setFontSize(14);
        titleRun.setColor("2c3e50");
        titleRun.setBold(true);
    }

    /**
     * 创建现代风格的报告标题
     */
    public static void createModernReportTitle(XWPFDocument document, String scaleName,
                                          WordTemplateConfig.TitleStyle titleStyle,
                                          WordTemplateConfig.DecorationStyle decorationStyle) {
        // 主标题
        XWPFParagraph mainTitlePara = document.createParagraph();
        mainTitlePara.setSpacingBefore(200);
        mainTitlePara.setSpacingAfter(150);
        mainTitlePara.setAlignment(ParagraphAlignment.CENTER);
        
        XWPFRun mainTitleRun = mainTitlePara.createRun();
        mainTitleRun.setText(scaleName + "测评报告");
        mainTitleRun.setFontFamily("微软雅黑");
        mainTitleRun.setFontSize(20);
        mainTitleRun.setColor("2c3e50");
        mainTitleRun.setBold(true);
        
        // 添加装饰线
        XWPFParagraph dividerPara = document.createParagraph();
        dividerPara.setSpacingBefore(80);
        dividerPara.setSpacingAfter(300);
        dividerPara.setAlignment(ParagraphAlignment.CENTER);
        
        XWPFRun dividerRun = dividerPara.createRun();
        dividerRun.setText("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━");
        dividerRun.setColor("e5e7eb");
        dividerRun.setFontSize(6);
    }

    /**
     * 创建现代风格的结果解释区域
     */
    public static void createModernResultSection(XWPFDocument document, String factorName, String standardScore,
                                            String depressionIndex, String interpretation, String suggestions,
                                            WordTemplateConfig.FactorStyle factorStyle,
                                            WordTemplateConfig.DecorationStyle decorationStyle) {
        // 因子名称
        XWPFParagraph factorPara = document.createParagraph();
        factorPara.setSpacingBefore(250);
        factorPara.setSpacingAfter(80);
        
        XWPFRun factorRun = factorPara.createRun();
        factorRun.setText("▶ " + factorName);
        factorRun.setFontFamily("微软雅黑");
        factorRun.setFontSize(14);
        factorRun.setColor("2c3e50");
        factorRun.setBold(true);
        
        // 标准分和抑郁指数
        if (standardScore != null || depressionIndex != null) {
            XWPFParagraph scorePara = document.createParagraph();
            scorePara.setSpacingBefore(80);
            scorePara.setSpacingAfter(80);
            
            XWPFRun scoreRun = scorePara.createRun();
            StringBuilder scoreText = new StringBuilder();
            if (standardScore != null) {
                scoreText.append("标准分: ").append(standardScore);
            }
            if (depressionIndex != null) {
                if (scoreText.length() > 0) scoreText.append("; ");
                scoreText.append("抑郁指数: ").append(depressionIndex);
            }
            scoreRun.setText(scoreText.toString());
            scoreRun.setFontFamily("微软雅黑");
            scoreRun.setFontSize(12);
            scoreRun.setColor("4b5563");
            scoreRun.setBold(false);
        }
        
        // 解释内容
        if (interpretation != null && !interpretation.trim().isEmpty()) {
            XWPFParagraph interpPara = document.createParagraph();
            interpPara.setSpacingBefore(80);
            interpPara.setSpacingAfter(80);
            
            XWPFRun interpRun = interpPara.createRun();
            interpRun.setText(interpretation);
            interpRun.setFontFamily("微软雅黑");
            interpRun.setFontSize(12);
            interpRun.setColor("4b5563");
            interpRun.setBold(false);
        }
        
        // 建议
        if (suggestions != null && !suggestions.trim().isEmpty()) {
            XWPFParagraph suggestPara = document.createParagraph();
            suggestPara.setSpacingBefore(80);
            suggestPara.setSpacingAfter(150);
            
            XWPFRun suggestRun = suggestPara.createRun();
            suggestRun.setText("【建议】" + suggestions);
            suggestRun.setFontFamily("微软雅黑");
            suggestRun.setFontSize(12);
            suggestRun.setColor("565EFF");
            suggestRun.setBold(true);
        }
    }
}
