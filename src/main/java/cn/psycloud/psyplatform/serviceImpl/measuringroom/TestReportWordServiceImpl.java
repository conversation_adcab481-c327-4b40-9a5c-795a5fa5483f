package cn.psycloud.psyplatform.serviceImpl.measuringroom;

import cn.hutool.core.date.DateUtil;
import cn.psycloud.psyplatform.config.WordTemplateConfig;
import cn.psycloud.psyplatform.dto.measuringroom.FactorExplainHierarchyDto;
import cn.psycloud.psyplatform.dto.measuringroom.ReportDto;
import cn.psycloud.psyplatform.dto.measuringroom.TestRecordDto;
import cn.psycloud.psyplatform.entity.measuringroom.TestRecordChartsEntity;
import cn.psycloud.psyplatform.service.measuringroom.TestRecordService;
import cn.psycloud.psyplatform.service.measuringroom.TestReportWordService;
import cn.psycloud.psyplatform.util.WordStyleUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.util.Units;
import org.apache.poi.xwpf.usermodel.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 测评报告Word生成服务实现类
 */
@Service
@Slf4j
public class TestReportWordServiceImpl implements TestReportWordService {

    @Autowired
    private TestRecordService testRecordService;

    @Autowired
    private WordTemplateConfig wordTemplateConfig;

    @Value("${file.location}")
    private String uploadPath;

    /**
     * 生成与网页一致的Word报告
     * @param recordId 测评记录ID
     * @param folderName 文件夹名称
     * @return 生成的文件路径
     */
    @Override
    public String generateWordReport(Integer recordId, String folderName) {
        log.info("开始生成Word报告，recordId: {}, folderName: {}", recordId, folderName);

        try {
            // 获取报告数据
            ReportDto reportData = testRecordService.getReport(recordId);
            if (reportData == null || reportData.getTestRecord() == null) {
                log.error("无法获取测评报告数据，recordId: {}", recordId);
                return "";
            }

            log.info("成功获取报告数据，因子数量: {}, 图表数量: {}",
                reportData.getListExplains() != null ? reportData.getListExplains().size() : 0,
                reportData.getListCharts() != null ? reportData.getListCharts().size() : 0);



            // 创建Word文档
            XWPFDocument document = new XWPFDocument();

            // 添加现代风格的报告标题
            WordStyleUtil.createModernReportTitle(document,
                reportData.getTestRecord().getScale().getScaleName(),
                wordTemplateConfig.getTitle(),
                wordTemplateConfig.getDecoration());

            // 添加现代风格的基本信息区域
            addModernBasicInfoSection(document, reportData.getTestRecord());

            // 添加现代风格的因子层级结构
            addModernFactorHierarchySection(document, reportData.getListExplains(), reportData.getListCharts());

            // 添加现代风格的报告总结
            WordStyleUtil.createReportSummary(document,
                reportData.getTestRecord().getScale().getScaleName(),
                wordTemplateConfig.getTitle(),
                wordTemplateConfig.getDecoration());

            // 保存文档
            String fileName = generateFileName(reportData.getTestRecord());
            String filePath = String.format("report/%s/%s", folderName, fileName);
            String fileAbsPath = uploadPath + filePath;

            // 确保目录存在
            File directory = new File(uploadPath + "report/" + folderName);
            if (!directory.exists()) {
                boolean created = directory.mkdirs();
                log.debug("创建目录: {}, 结果: {}", directory.getAbsolutePath(), created);
            }

            // 写入文件
            try (FileOutputStream out = new FileOutputStream(fileAbsPath)) {
                document.write(out);
            }
            document.close();

            log.info("Word报告生成成功: {}, 文件大小: {} bytes", filePath, new File(fileAbsPath).length());
            return filePath;

        } catch (Exception e) {
            log.error("生成Word报告失败，recordId: {}", recordId, e);
            return "";
        }
    }

    /**
     * 生成Word报告并直接下载
     * @param response HTTP响应
     * @param request HTTP请求
     * @param recordId 测评记录ID
     * @return 生成的文件路径
     */
    @Override
    public String generateAndDownloadWordReport(HttpServletResponse response, HttpServletRequest request, Integer recordId) {
        String folderName = DateUtil.format(new Date(), "yyyyMMddHHmmssSSS");
        return generateWordReport(recordId, folderName);
    }

    /**
     * 生成测试Word模板（用于预览新样式）
     * @return 生成的文件路径
     */
    public String generateTestWordTemplate() {
        log.info("开始生成测试Word模板");

        try {
            // 创建Word文档
            XWPFDocument document = new XWPFDocument();

            // 添加现代风格的报告标题
            WordStyleUtil.createModernReportTitle(document, "抑郁自评量表(SDS)",
                wordTemplateConfig.getTitle(),
                wordTemplateConfig.getDecoration());

            // 添加测试基本信息
            String[][] testInfoData = {
                {"姓名", "平台管理员"},
                {"测试日期", "2025-07-23"},
                {"所属组织", "EAP档案系统"},
                {"耗时", "1分5秒"},
                {"测试项目", "抑郁自评量表(SDS)"},
                {"测试状态", "已完成"}
            };

            WordStyleUtil.createModernInfoCardGrid(document, testInfoData,
                wordTemplateConfig.getTable(),
                wordTemplateConfig.getDecoration());

            // 添加测试结果解释
            WordStyleUtil.createModernSectionTitle(document, "💡", "结果解释及建议",
                wordTemplateConfig.getTable(), wordTemplateConfig.getDecoration());

            // 添加测试因子分析
            WordStyleUtil.createModernResultSection(document, "总分", "58.75",
                "0.59(异常)", 
                "抑郁是一种常见的心理状态，主要表现为悲观、绝望、易怒、饮食习惯改变、失眠、兴趣减退或注意力不集中、自杀念头、抗拒履行社会职责、极度疲劳、反应迟钝或敏感等。对每个人来说，有抑郁想法是很常见的事情，关键是我们如何调整自己。",
                "用积极乐观的态度去对待生活，有意识地让快乐与身边的事物联系起来。当遇到不愉快的事情时，学会调整心态。你可以向好朋友倾诉，或者把注意力转移到你喜欢做的事情上。相信所有的不愉快都会过去，明天将是崭新的一天。",
                wordTemplateConfig.getFactor(),
                wordTemplateConfig.getDecoration());

            // 添加图表分析标题
            WordStyleUtil.createModernSectionTitle(document, "📊", "图表分析",
                wordTemplateConfig.getTable(), wordTemplateConfig.getDecoration());

            // 添加图表容器
            WordStyleUtil.createModernChartContainer(document, "图表分析",
                wordTemplateConfig.getChart(),
                wordTemplateConfig.getDecoration());

            // 添加报告总结
            WordStyleUtil.createModernSectionTitle(document, "📋", "报告总结",
                wordTemplateConfig.getTable(), wordTemplateConfig.getDecoration());

            XWPFParagraph summaryPara = document.createParagraph();
            summaryPara.setSpacingBefore(150);
            summaryPara.setSpacingAfter(200);
            
            XWPFRun summaryRun = summaryPara.createRun();
            summaryRun.setText("本报告基于抑郁自评量表(SDS)测评结果生成，提供了详细的分析和建议。如需进一步了解，建议咨询专业心理咨询师。");
            summaryRun.setFontFamily("微软雅黑");
            summaryRun.setFontSize(12);
            summaryRun.setColor("4b5563");
            summaryRun.setBold(false);

            // 添加免责声明
            XWPFParagraph disclaimerPara = document.createParagraph();
            disclaimerPara.setSpacingBefore(200);
            disclaimerPara.setSpacingAfter(100);
            disclaimerPara.setAlignment(ParagraphAlignment.CENTER);
            
            XWPFRun disclaimerRun = disclaimerPara.createRun();
            disclaimerRun.setText("▲ 本报告仅供参考，不能替代专业心理诊断");
            disclaimerRun.setFontFamily("微软雅黑");
            disclaimerRun.setFontSize(10);
            disclaimerRun.setColor("f59e0b");
            disclaimerRun.setBold(false);

            // 保存文档
            String fileName = "test_template_" + DateUtil.format(new Date(), "yyyyMMddHHmmssSSS") + ".docx";
            String filePath = "report/test/" + fileName;
            String fileAbsPath = uploadPath + filePath;

            // 确保目录存在
            File directory = new File(uploadPath + "report/test");
            if (!directory.exists()) {
                boolean created = directory.mkdirs();
                log.debug("创建目录: {}, 结果: {}", directory.getAbsolutePath(), created);
            }

            // 写入文件
            try (FileOutputStream out = new FileOutputStream(fileAbsPath)) {
                document.write(out);
            }
            document.close();

            log.info("测试Word模板生成成功: {}, 文件大小: {} bytes", filePath, new File(fileAbsPath).length());
            return filePath;

        } catch (Exception e) {
            log.error("生成测试Word模板失败", e);
            return "";
        }
    }

    /**
     * 添加现代风格的基本信息区域
     */
    private void addModernBasicInfoSection(XWPFDocument document, TestRecordDto testRecord) {
        if (document == null || testRecord == null) {
            log.warn("文档或测试记录为空，跳过基本信息区域生成");
            return;
        }

        try {
            // 准备基本信息数据
            String userName = "未知用户";
            String testDate = "未知日期";
            String structName = "未知组织";
            String scaleName = "未知测试";

            if (testRecord.getUser() != null) {
                userName = testRecord.getUser().getRealName() != null && !testRecord.getUser().getRealName().isEmpty()
                    ? testRecord.getUser().getRealName() :
                    (testRecord.getUser().getLoginName() != null ? testRecord.getUser().getLoginName() : "未知用户");
                structName = testRecord.getUser().getStructName() != null ? testRecord.getUser().getStructName() : "未知组织";
            }

            if (testRecord.getStartTime() != null) {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                testDate = sdf.format(testRecord.getStartTime());
            }

            if (testRecord.getScale() != null && testRecord.getScale().getScaleName() != null) {
                scaleName = testRecord.getScale().getScaleName();
            }

            String costTime = formatSeconds(testRecord.getTimeInterval());

            String[][] infoData = {
                {"姓名", userName},
                {"测试日期", testDate},
                {"所属组织", structName},
                {"耗时", costTime},
                {"测试项目", scaleName},
                {"测试状态", "已完成"}
            };

            // 使用新的现代卡片网格
            WordStyleUtil.createModernInfoCardGrid(document, infoData,
                wordTemplateConfig.getTable(),
                wordTemplateConfig.getDecoration());

        } catch (Exception e) {
            log.error("生成基本信息区域失败", e);
            // 添加错误提示
            try {
                XWPFParagraph errorPara = document.createParagraph();
                XWPFRun errorRun = errorPara.createRun();
                errorRun.setText("基本信息生成失败");
                errorRun.setColor("ef4444");
            } catch (Exception ignored) {
                // 忽略二次异常
            }
        }
    }

    /**
     * 添加现代风格的因子层级结构
     */
    private void addModernFactorHierarchySection(XWPFDocument document, List<FactorExplainHierarchyDto> factors, List<TestRecordChartsEntity> charts) {
        if (factors == null || factors.isEmpty()) {
            return;
        }

        // 创建章节标题
        WordStyleUtil.createModernSectionTitle(document, "💡", "结果解释及建议",
            wordTemplateConfig.getTable(), wordTemplateConfig.getDecoration());

        // 按照前端相同的逻辑组织因子数据
        OrganizedFactorData organizedData = organizeFactorData(factors);

        // 1. 显示顶层因子
        if (!organizedData.topLevelFactors.isEmpty()) {
            for (FactorExplainHierarchyDto topLevelFactor : organizedData.topLevelFactors) {
                WordStyleUtil.createModernFactorSection(document,
                    topLevelFactor.getFactorName(),
                    topLevelFactor.getInterpretation(),
                    wordTemplateConfig.getFactor(),
                    wordTemplateConfig.getDecoration(),
                    1);
            }
            // 插入顶层图表
            insertBeautifulCharts(document, charts, "topLevel");
        }

        // 2. 显示父因子组
        for (ParentGroup parentGroup : organizedData.parentGroups) {
            WordStyleUtil.createModernFactorSection(document,
                parentGroup.parentName,
                null,
                wordTemplateConfig.getFactor(),
                wordTemplateConfig.getDecoration(),
                2);

            // 插入父因子组图表
            insertBeautifulCharts(document, charts, "parent_" + parentGroup.parentId);

            // 处理子因子
            for (FactorExplainHierarchyDto child : parentGroup.children) {
                WordStyleUtil.createModernFactorSection(document,
                    child.getFactorName(),
                    child.getInterpretation(),
                    wordTemplateConfig.getFactor(),
                    wordTemplateConfig.getDecoration(),
                    3);
            }
        }

        // 3. 显示独立因子
        if (!organizedData.independentFactors.isEmpty()) {
            // 插入独立因子图表
            insertBeautifulCharts(document, charts, "independent");

            for (FactorExplainHierarchyDto independentFactor : organizedData.independentFactors) {
                WordStyleUtil.createModernFactorSection(document,
                    independentFactor.getFactorName(),
                    independentFactor.getInterpretation(),
                    wordTemplateConfig.getFactor(),
                    wordTemplateConfig.getDecoration(),
                    2);
            }
        }
    }

    /**
     * 添加美观的基本信息区域
     */
    private void addBeautifulBasicInfoSection(XWPFDocument document, TestRecordDto testRecord) {
        if (document == null || testRecord == null) {
            log.warn("文档或测试记录为空，跳过基本信息区域生成");
            return;
        }

        try {
            // 准备基本信息数据
            String userName = "未知用户";
            String testDate = "未知日期";
            String structName = "未知组织";
            String scaleName = "未知测试";

            if (testRecord.getUser() != null) {
                userName = testRecord.getUser().getRealName() != null && !testRecord.getUser().getRealName().isEmpty()
                    ? testRecord.getUser().getRealName() :
                    (testRecord.getUser().getLoginName() != null ? testRecord.getUser().getLoginName() : "未知用户");
                structName = testRecord.getUser().getStructName() != null ? testRecord.getUser().getStructName() : "未知组织";
            }

            if (testRecord.getStartTime() != null) {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                testDate = sdf.format(testRecord.getStartTime());
            }

            if (testRecord.getScale() != null && testRecord.getScale().getScaleName() != null) {
                scaleName = testRecord.getScale().getScaleName();
            }

            String costTime = formatSeconds(testRecord.getTimeInterval());

            String[][] infoData = {
                {"姓名", userName},
                {"测试日期", testDate},
                {"所属组织", structName},
                {"耗时", costTime},
                {"测试项目", scaleName},
                {"测试状态", "已完成"}
            };

            // 使用新的美观卡片网格
            WordStyleUtil.createInfoCardGrid(document, infoData,
                wordTemplateConfig.getTable(),
                wordTemplateConfig.getDecoration());

        } catch (Exception e) {
            log.error("生成基本信息区域失败", e);
            // 添加错误提示
            try {
                XWPFParagraph errorPara = document.createParagraph();
                XWPFRun errorRun = errorPara.createRun();
                errorRun.setText("基本信息生成失败");
                errorRun.setColor("ef4444");
            } catch (Exception ignored) {
                // 忽略二次异常
            }
        }
    }

    /**
     * 添加美观的因子层级结构
     */
    private void addBeautifulFactorHierarchySection(XWPFDocument document, List<FactorExplainHierarchyDto> factors, List<TestRecordChartsEntity> charts) {
        if (factors == null || factors.isEmpty()) {
            return;
        }

        // 创建章节标题
        WordStyleUtil.createSectionTitle(document, "💡", "结果解释及建议",
            wordTemplateConfig.getTable(), wordTemplateConfig.getDecoration());

        // 按照前端相同的逻辑组织因子数据
        OrganizedFactorData organizedData = organizeFactorData(factors);

        // 1. 显示顶层因子
        if (!organizedData.topLevelFactors.isEmpty()) {
            for (FactorExplainHierarchyDto topLevelFactor : organizedData.topLevelFactors) {
                WordStyleUtil.createFactorLevelSection(document,
                    topLevelFactor.getFactorName(),
                    topLevelFactor.getInterpretation(),
                    wordTemplateConfig.getFactor(),
                    wordTemplateConfig.getDecoration(),
                    1);
            }
            // 插入顶层图表
            insertBeautifulCharts(document, charts, "topLevel");
        }

        // 2. 显示父因子组
        for (ParentGroup parentGroup : organizedData.parentGroups) {
            WordStyleUtil.createFactorLevelSection(document,
                parentGroup.parentName,
                null,
                wordTemplateConfig.getFactor(),
                wordTemplateConfig.getDecoration(),
                2);

            // 插入父因子组图表
            insertBeautifulCharts(document, charts, "parent_" + parentGroup.parentId);

            // 处理子因子
            for (FactorExplainHierarchyDto child : parentGroup.children) {
                WordStyleUtil.createFactorLevelSection(document,
                    child.getFactorName(),
                    child.getInterpretation(),
                    wordTemplateConfig.getFactor(),
                    wordTemplateConfig.getDecoration(),
                    3);
            }
        }

        // 3. 显示独立因子
        if (!organizedData.independentFactors.isEmpty()) {
            // 插入独立因子图表
            insertBeautifulCharts(document, charts, "independent");

            for (FactorExplainHierarchyDto independentFactor : organizedData.independentFactors) {
                WordStyleUtil.createFactorLevelSection(document,
                    independentFactor.getFactorName(),
                    independentFactor.getInterpretation(),
                    wordTemplateConfig.getFactor(),
                    wordTemplateConfig.getDecoration(),
                    2);
            }
        }
    }

    /**
     * 插入美观的图表
     */
    private void insertBeautifulCharts(XWPFDocument document, List<TestRecordChartsEntity> charts, String factorType) {
        if (charts == null || charts.isEmpty()) {
            return;
        }

        // 获取并排序指定类型的图表
        List<TestRecordChartsEntity> filteredCharts = charts.stream()
            .filter(chart -> chart.getFactorType() != null && chart.getFactorType().equals(factorType))
            .sorted((c1, c2) -> {
                int orderCompare = Integer.compare(
                    c1.getChartOrder() != null ? c1.getChartOrder() : 0,
                    c2.getChartOrder() != null ? c2.getChartOrder() : 0
                );
                if (orderCompare != 0) return orderCompare;

                return Integer.compare(
                    c1.getChartIndex() != null ? c1.getChartIndex() : 0,
                    c2.getChartIndex() != null ? c2.getChartIndex() : 0
                );
            })
            .collect(java.util.stream.Collectors.toList());

        for (TestRecordChartsEntity chart : filteredCharts) {
            insertBeautifulChart(document, chart);
        }
    }

    /**
     * 插入单个美观图表
     */
    private void insertBeautifulChart(XWPFDocument document, TestRecordChartsEntity chart) {
        try {
            // 确保路径正确拼接
            String chartPath = uploadPath;
            if (!chartPath.endsWith("/") && !chartPath.endsWith("\\")) {
                chartPath += "/";
            }
            chartPath += "charts/" + chart.getChartsImg();

            File chartFile = new File(chartPath);
            WordTemplateConfig.ChartStyle chartStyle = wordTemplateConfig.getChart();
            WordTemplateConfig.DecorationStyle decorationStyle = wordTemplateConfig.getDecoration();

            if (chartFile.exists()) {
                // 创建美观的图表容器
                String chartTitle = getChartTitle(chart.getChartType()) + "分析";
                WordStyleUtil.createChartContainer(document, chartTitle, chartStyle, decorationStyle);

                // 插入图表
                XWPFParagraph chartParagraph = document.createParagraph();
                chartParagraph.setAlignment(ParagraphAlignment.CENTER);
                chartParagraph.setSpacingBefore(200);
                chartParagraph.setSpacingAfter(chartStyle.getSpacingAfter());

                XWPFRun chartRun = chartParagraph.createRun();
                try (FileInputStream chartStream = new FileInputStream(chartFile)) {
                    // 根据图表类型调整大小
                    WordStyleUtil.ChartSize chartSize = WordStyleUtil.getChartSize(
                        chart.getChartType() != null ? chart.getChartType() : "default",
                        chartStyle
                    );

                    chartRun.addPicture(chartStream, XWPFDocument.PICTURE_TYPE_PNG, chart.getChartsImg(),
                        Units.toEMU(chartSize.getWidth()), Units.toEMU(chartSize.getHeight()));
                }

                // 添加图表底部装饰
                XWPFParagraph bottomBorder = document.createParagraph();
                bottomBorder.setAlignment(ParagraphAlignment.CENTER);
                bottomBorder.setSpacingAfter(400);

                XWPFRun bottomRun = bottomBorder.createRun();
                bottomRun.setText("└─────────────────────────────────────────────────────────────────┘");
                bottomRun.setColor(chartStyle.getBorderColor());
                bottomRun.setFontSize(8);

            } else {
                log.warn("图表文件不存在: {}, 完整路径: {}", chart.getChartsImg(), chartFile.getAbsolutePath());
                addBeautifulChartPlaceholder(document, "图表文件不存在: " + chart.getChartsImg());
            }
        } catch (Exception e) {
            log.error("插入图表失败: {}", chart.getChartsImg(), e);
            addBeautifulChartPlaceholder(document, "图表插入失败: " + e.getMessage());
        }
    }

    /**
     * 添加美观的图表占位符
     */
    private void addBeautifulChartPlaceholder(XWPFDocument document, String message) {
        XWPFParagraph placeholder = document.createParagraph();
        placeholder.setAlignment(ParagraphAlignment.CENTER);
        placeholder.setSpacingBefore(300);
        placeholder.setSpacingAfter(300);

        XWPFRun placeholderRun = placeholder.createRun();
        placeholderRun.setText("⚠ " + message + " ⚠");
        placeholderRun.setColor("f59e0b");
        placeholderRun.setItalic(true);
        placeholderRun.setFontSize(12);
        placeholderRun.setBold(true);
    }

    /**
     * 添加卡片式基本信息（保留兼容性）
     */
    private void addBasicInfoCards(XWPFDocument document, TestRecordDto testRecord) {
        // 准备数据
        String userName = testRecord.getUser().getRealName() != null && !testRecord.getUser().getRealName().isEmpty()
            ? testRecord.getUser().getRealName() : testRecord.getUser().getLoginName();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String testDate = sdf.format(testRecord.getStartTime());
        String costTime = formatSeconds(testRecord.getTimeInterval());

        // 创建信息卡片表格（2行3列，模仿网页版布局）
        XWPFTable cardTable = document.createTable(2, 3);

        // 设置表格样式
        WordStyleUtil.applyTableStyle(cardTable, wordTemplateConfig.getTable());

        // 设置表格宽度
        cardTable.setWidth("100%");

        // 第一行：姓名、测试日期、所属组织
        addInfoCard(cardTable, 0, 0, "姓名", userName, "3b82f6");
        addInfoCard(cardTable, 0, 1, "测试日期", testDate, "10b981");
        addInfoCard(cardTable, 0, 2, "所属组织", testRecord.getUser().getStructName(), "f59e0b");

        // 第二行：耗时、测试项目、测试状态
        addInfoCard(cardTable, 1, 0, "耗时", costTime, "ef4444");
        addInfoCard(cardTable, 1, 1, "测试项目", testRecord.getScale().getScaleName(), "8b5cf6");
        addInfoCard(cardTable, 1, 2, "测试状态", "已完成", "06b6d4");
    }

    /**
     * 添加信息卡片
     */
    private void addInfoCard(XWPFTable table, int row, int col, String label, String value, String accentColor) {
        if (label.isEmpty()) return;

        XWPFTableCell cell = table.getRow(row).getCell(col);
        WordTemplateConfig.TableStyle tableStyle = wordTemplateConfig.getTable();

        // 使用新的信息卡片创建方法
        WordStyleUtil.createInfoCard(cell, label, value, accentColor, tableStyle);
    }











    /**
     * 获取图表标题
     */
    private String getChartTitle(String chartType) {
        switch (chartType) {
            case "column":
                return "柱状图";
            case "line":
                return "折线图";
            case "pie":
                return "饼图";
            case "bar":
                return "条形图";
            default:
                return "图表";
        }
    }

    /**
     * 生成文件名
     */
    private String generateFileName(TestRecordDto testRecord) {
        String userName = testRecord.getUser().getRealName() != null && !testRecord.getUser().getRealName().isEmpty() 
            ? testRecord.getUser().getRealName() : testRecord.getUser().getLoginName();
        return testRecord.getId() + "_" + userName + "_" + testRecord.getScale().getScaleName() + ".docx";
    }

    /**
     * 格式化秒数为时间字符串
     */
    private String formatSeconds(Integer seconds) {
        if (seconds == null || seconds <= 0) {
            return "0分0秒";
        }

        try {
            int minutes = seconds / 60;
            int remainingSeconds = seconds % 60;

            if (minutes > 0) {
                return minutes + "分" + remainingSeconds + "秒";
            } else {
                return remainingSeconds + "秒";
            }
        } catch (Exception e) {
            log.warn("格式化时间失败: {}", seconds, e);
            return "未知时长";
        }
    }

    /**
     * 按照前端相同的逻辑组织因子数据
     */
    private OrganizedFactorData organizeFactorData(List<FactorExplainHierarchyDto> factors) {
        OrganizedFactorData result = new OrganizedFactorData();

        for (FactorExplainHierarchyDto factor : factors) {
            if (factor.getChildren() != null && !factor.getChildren().isEmpty()) {
                // 有子因子的顶层因子
                result.topLevelFactors.add(factor);

                // 添加到父因子组
                ParentGroup parentGroup = new ParentGroup();
                parentGroup.parentId = factor.getFactorId();
                parentGroup.parentName = factor.getFactorName();
                parentGroup.children = factor.getChildren();
                result.parentGroups.add(parentGroup);
            } else {
                // 独立因子
                result.independentFactors.add(factor);
            }
        }

        return result;
    }

    /**
     * 组织后的因子数据结构
     */
    private static class OrganizedFactorData {
        List<FactorExplainHierarchyDto> topLevelFactors = new ArrayList<>();
        List<ParentGroup> parentGroups = new ArrayList<>();
        List<FactorExplainHierarchyDto> independentFactors = new ArrayList<>();
    }

    /**
     * 父因子组
     */
    private static class ParentGroup {
        Integer parentId;
        String parentName;
        List<FactorExplainHierarchyDto> children;
    }

}
