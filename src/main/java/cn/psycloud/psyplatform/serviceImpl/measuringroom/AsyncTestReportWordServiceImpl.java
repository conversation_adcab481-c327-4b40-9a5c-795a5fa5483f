package cn.psycloud.psyplatform.serviceImpl.measuringroom;

import cn.psycloud.psyplatform.service.measuringroom.AsyncTestReportWordService;
import cn.psycloud.psyplatform.service.measuringroom.TestReportWordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

/**
 * 异步测评报告Word生成服务实现类
 */
@Service
@Slf4j
public class AsyncTestReportWordServiceImpl implements AsyncTestReportWordService {

    @Autowired
    private TestReportWordService testReportWordService;

    // 任务状态缓存
    private final ConcurrentHashMap<String, String> taskStatusMap = new ConcurrentHashMap<>();

    /**
     * 异步生成Word报告
     */
    @Override
    @Async("taskExecutor")
    public CompletableFuture<String> generateWordReportAsync(Integer recordId, String folderName) {
        String taskId = generateTaskId(recordId, folderName);
        taskStatusMap.put(taskId, "PROCESSING");
        
        try {
            log.info("开始异步生成Word报告，recordId: {}, taskId: {}", recordId, taskId);
            String result = testReportWordService.generateWordReport(recordId, folderName);
            
            if (result != null && !result.isEmpty()) {
                taskStatusMap.put(taskId, "COMPLETED");
                log.info("异步生成Word报告成功，recordId: {}, taskId: {}", recordId, taskId);
            } else {
                taskStatusMap.put(taskId, "FAILED");
                log.error("异步生成Word报告失败，recordId: {}, taskId: {}", recordId, taskId);
            }
            
            return CompletableFuture.completedFuture(result);
        } catch (Exception e) {
            taskStatusMap.put(taskId, "ERROR");
            log.error("异步生成Word报告异常，recordId: {}, taskId: {}", recordId, taskId, e);
            return CompletableFuture.completedFuture("");
        }
    }

    /**
     * 批量异步生成Word报告
     */
    @Override
    @Async("taskExecutor")
    public CompletableFuture<List<String>> generateBatchWordReportsAsync(List<Integer> recordIds, String folderName) {
        String batchTaskId = "batch_" + System.currentTimeMillis();
        taskStatusMap.put(batchTaskId, "PROCESSING");
        
        try {
            log.info("开始批量异步生成Word报告，数量: {}, taskId: {}", recordIds.size(), batchTaskId);
            
            List<CompletableFuture<String>> futures = recordIds.stream()
                .map(recordId -> generateWordReportAsync(recordId, folderName))
                .collect(Collectors.toList());
            
            CompletableFuture<Void> allFutures = CompletableFuture.allOf(
                futures.toArray(new CompletableFuture[0])
            );
            
            List<String> results = allFutures.thenApply(v ->
                futures.stream()
                    .map(CompletableFuture::join)
                    .collect(Collectors.toList())
            ).get();
            
            taskStatusMap.put(batchTaskId, "COMPLETED");
            log.info("批量异步生成Word报告完成，taskId: {}", batchTaskId);
            
            return CompletableFuture.completedFuture(results);
        } catch (Exception e) {
            taskStatusMap.put(batchTaskId, "ERROR");
            log.error("批量异步生成Word报告异常，taskId: {}", batchTaskId, e);
            return CompletableFuture.completedFuture(Collections.emptyList());
        }
    }

    /**
     * 获取生成任务状态
     */
    @Override
    public String getTaskStatus(String taskId) {
        return taskStatusMap.getOrDefault(taskId, "NOT_FOUND");
    }

    /**
     * 生成任务ID
     */
    private String generateTaskId(Integer recordId, String folderName) {
        return String.format("word_report_%d_%s_%d", recordId, folderName, System.currentTimeMillis());
    }
}
